using System;
using System.IO;
using System.Windows.Forms;

namespace XLS2PDF
{
    class Program
    {
        [STAThread]
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("XLS2PDF 转换工具");
                Console.WriteLine("================");

                // 创建文件选择对话框
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Title = "选择Excel文件";
                openFileDialog.Filter = "Excel文件|*.xls;*.xlsx|Excel 97-2003 (*.xls)|*.xls|Excel 2007-2019 (*.xlsx)|*.xlsx";
                openFileDialog.FilterIndex = 1;
                openFileDialog.RestoreDirectory = true;

                Console.WriteLine("请在弹出的对话框中选择Excel文件...");

                // 显示文件选择对话框
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string excelFilePath = openFileDialog.FileName;
                    Console.WriteLine($"选择的文件: {excelFilePath}");

                    // 检查文件是否存在
                    if (!File.Exists(excelFilePath))
                    {
                        Console.WriteLine("错误：选择的文件不存在！");
                        Console.WriteLine("按任意键退出...");
                        Console.ReadKey();
                        return;
                    }

                    // 生成PDF文件路径（与原文件同目录同名，扩展名改为.pdf）
                    string pdfFilePath = Path.ChangeExtension(excelFilePath, ".pdf");
                    Console.WriteLine($"目标PDF文件: {pdfFilePath}");

                    // 转换Excel到PDF
                    Console.WriteLine("正在转换文件，请稍候...");
                    ConvertExcelToPdf(excelFilePath, pdfFilePath);

                    Console.WriteLine("========================================");
                    Console.WriteLine("转换完成！");
                    Console.WriteLine($"PDF文件已保存到: {pdfFilePath}");
                    Console.WriteLine("========================================");
                    Console.WriteLine("按任意键退出...");
                    Console.ReadKey();
                }
                else
                {
                    Console.WriteLine("未选择文件，程序退出。");
                    Console.WriteLine("按任意键退出...");
                    Console.ReadKey();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("========================================");
                Console.WriteLine($"发生错误: {ex.Message}");
                Console.WriteLine("========================================");
                Console.WriteLine("详细错误信息:");
                Console.WriteLine(ex.ToString());
                Console.WriteLine("========================================");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        static void ConvertExcelToPdf(string excelFilePath, string pdfFilePath)
        {
            try
            {
                // 检查Spire.XLS.dll是否存在
                string dllPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Spire.XLS.dll");
                if (!File.Exists(dllPath))
                {
                    // 尝试在res文件夹中查找
                    dllPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Spire.XLS.dll");
                    if (!File.Exists(dllPath))
                    {
                        throw new FileNotFoundException("未找到 Spire.XLS.dll 文件。请确保该文件存在于程序目录或res文件夹中。");
                    }
                }

                Console.WriteLine($"使用 Spire.XLS.dll: {dllPath}");

                // 动态加载Spire.XLS程序集
                var assembly = System.Reflection.Assembly.LoadFrom(dllPath);
                var workbookType = assembly.GetType("Spire.Xls.Workbook");
                
                if (workbookType == null)
                {
                    throw new Exception("无法找到 Spire.Xls.Workbook 类型。请检查 Spire.XLS.dll 版本。");
                }

                // 创建Workbook实例
                var workbook = Activator.CreateInstance(workbookType);

                // 调用LoadFromFile方法
                var loadMethod = workbookType.GetMethod("LoadFromFile", new Type[] { typeof(string) });
                if (loadMethod == null)
                {
                    throw new Exception("无法找到 LoadFromFile 方法。");
                }
                loadMethod.Invoke(workbook, new object[] { excelFilePath });

                // 获取FileFormat枚举
                var fileFormatType = assembly.GetType("Spire.Xls.FileFormat");
                if (fileFormatType == null)
                {
                    throw new Exception("无法找到 FileFormat 枚举。");
                }
                var pdfFormat = Enum.Parse(fileFormatType, "PDF");

                // 调用SaveToFile方法
                var saveMethod = workbookType.GetMethod("SaveToFile", new Type[] { typeof(string), fileFormatType });
                if (saveMethod == null)
                {
                    throw new Exception("无法找到 SaveToFile 方法。");
                }
                saveMethod.Invoke(workbook, new object[] { pdfFilePath, pdfFormat });

                Console.WriteLine("Excel文件已成功转换为PDF！");
            }
            catch (Exception ex)
            {
                throw new Exception($"转换过程中发生错误: {ex.Message}", ex);
            }
        }
    }
}
