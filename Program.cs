using System;
using System.IO;
using System.Windows.Forms;
using Spire.Xls;

namespace XLS2PDF
{
    class Program
    {
        [STAThread]
        static void Main(string[] args)
        {
            try
            {
                // 创建文件选择对话框
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Title = "选择Excel文件";
                openFileDialog.Filter = "Excel文件|*.xls;*.xlsx|Excel 97-2003 (*.xls)|*.xls|Excel 2007-2019 (*.xlsx)|*.xlsx";
                openFileDialog.FilterIndex = 1;
                openFileDialog.RestoreDirectory = true;

                // 显示文件选择对话框
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string excelFilePath = openFileDialog.FileName;
                    Console.WriteLine($"选择的文件: {excelFilePath}");

                    // 生成PDF文件路径（与原文件同目录同名，扩展名改为.pdf）
                    string pdfFilePath = Path.ChangeExtension(excelFilePath, ".pdf");

                    // 转换Excel到PDF
                    ConvertExcelToPdf(excelFilePath, pdfFilePath);

                    Console.WriteLine($"转换完成！PDF文件已保存到: {pdfFilePath}");
                    Console.WriteLine("按任意键退出...");
                    Console.ReadKey();
                }
                else
                {
                    Console.WriteLine("未选择文件，程序退出。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        static void ConvertExcelToPdf(string excelFilePath, string pdfFilePath)
        {
            try
            {
                // 创建Workbook对象并加载Excel文件
                Workbook workbook = new Workbook();
                workbook.LoadFromFile(excelFilePath);

                // 转换为PDF
                workbook.SaveToFile(pdfFilePath, FileFormat.PDF);

                Console.WriteLine("Excel文件已成功转换为PDF！");
            }
            catch (Exception ex)
            {
                throw new Exception($"转换过程中发生错误: {ex.Message}", ex);
            }
        }
    }
}
