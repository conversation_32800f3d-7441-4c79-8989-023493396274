@echo off
echo 正在构建XLS2PDF项目...

REM 检查是否安装了 .NET SDK
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到 .NET SDK！
    echo 请先安装 .NET 6.0 SDK：https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo 检测到 .NET SDK，版本：
dotnet --version

REM 清理之前的构建
echo 清理之前的构建...
dotnet clean

REM 构建项目
echo 构建项目...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

REM 发布为单个可执行文件
echo 正在发布为单个可执行文件...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ./publish /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true
if %errorlevel% neq 0 (
    echo 发布失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建完成！
echo 可执行文件位置: ./publish/XLS2PDF.exe
echo ========================================
pause
