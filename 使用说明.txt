XLS2PDF 转换工具使用说明
==========================

一、安装前准备
--------------
1. 确保您的计算机已安装 Windows 7 或更高版本
2. 下载并安装 .NET 6.0 SDK
   - 访问：https://dotnet.microsoft.com/download/dotnet/6.0
   - 选择 "Download .NET 6.0 SDK" (不是 Runtime)
   - 下载适合您系统的版本（通常是 x64）
   - 安装完成后重启计算机

二、构建可执行文件
------------------
方法一：使用批处理文件（推荐）
1. 双击运行 "build.bat" 文件
2. 等待构建完成
3. 在 "publish" 文件夹中找到 "XLS2PDF.exe"

方法二：手动构建
1. 打开命令提示符（cmd）
2. 切换到项目目录
3. 运行以下命令：
   dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ./publish /p:PublishSingleFile=true

三、使用程序
------------
1. 双击运行 "XLS2PDF.exe"
2. 在弹出的文件选择对话框中选择要转换的 Excel 文件（.xls 或 .xlsx）
3. 点击"确定"
4. 程序自动将文件转换为 PDF 格式
5. 转换后的 PDF 文件保存在原 Excel 文件相同的目录中，文件名相同（扩展名改为 .pdf）

四、注意事项
------------
1. 确保 Spire.XLS.dll 文件存在于 res 文件夹中
2. 支持的文件格式：.xls, .xlsx
3. 转换后的 PDF 文件会覆盖同名的现有 PDF 文件
4. 如果转换失败，请检查 Excel 文件是否损坏或被其他程序占用

五、故障排除
------------
问题1：提示"未找到 .NET SDK"
解决：重新安装 .NET 6.0 SDK，确保安装的是 SDK 而不是 Runtime

问题2：提示"未找到 Spire.XLS.dll"
解决：确保 Spire.XLS.dll 文件存在于 res 文件夹中

问题3：转换失败
解决：
- 检查 Excel 文件是否损坏
- 确保文件没有被其他程序打开
- 检查目标目录是否有写入权限

问题4：程序无法启动
解决：
- 确保已安装 .NET 6.0 运行时
- 尝试以管理员身份运行

六、文件说明
------------
- Program.cs: 主程序源代码
- Program_Simple.cs: 简化版源代码（使用反射加载DLL）
- XLS2PDF.csproj: 项目配置文件
- build.bat: 自动构建脚本
- res/Spire.XLS.dll: Spire.XLS 库文件
- publish/XLS2PDF.exe: 最终可执行文件（构建后生成）
