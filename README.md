# XLS2PDF 转换工具

这是一个使用 Spire.XLS.dll 将 Excel 文件转换为 PDF 的 C# 控制台应用程序。

## 功能特性

- 支持 .xls 和 .xlsx 文件格式
- 自动打开文件选择对话框
- 转换后的 PDF 文件保存在原文件相同目录，文件名相同（扩展名改为 .pdf）
- 简单易用的图形界面

## 使用方法

1. 运行程序
2. 在弹出的文件选择对话框中选择要转换的 Excel 文件
3. 程序自动将文件转换为 PDF 格式
4. 转换完成后显示保存路径

## 构建和打包

### 前提条件

**必须先安装 .NET 6.0 SDK**
1. 访问：https://dotnet.microsoft.com/download/dotnet/6.0
2. 下载并安装 ".NET 6.0 SDK" (不是 Runtime)
3. 安装完成后重启命令提示符
4. 验证安装：在命令提示符中运行 `dotnet --version`

### 方法一：使用批处理文件（推荐）

**安装 .NET SDK 后**，直接双击运行 `build.bat` 文件，它会：
1. 清理之前的构建
2. 构建项目
3. 发布为单个可执行文件
4. 生成的 exe 文件位于 `./publish/XLS2PDF.exe`

### 方法二：手动命令行构建

2. **构建项目**
   ```bash
   dotnet build --configuration Release
   ```

3. **发布为单个可执行文件**
   ```bash
   dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ./publish /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true
   ```

### 方法三：使用 Visual Studio

1. 在 Visual Studio 中打开项目
2. 右键点击项目 → 发布
3. 选择"文件夹"作为发布目标
4. 配置发布设置：
   - 目标运行时：win-x64
   - 部署模式：独立
   - 生成单个文件：是

## 文件结构

```
xls2pdf/
├── Program.cs          # 主程序代码
├── XLS2PDF.csproj     # 项目文件
├── build.bat          # 构建脚本
├── res/
│   └── Spire.XLS.dll  # Spire.XLS 库文件
└── publish/           # 发布输出目录（构建后生成）
    └── XLS2PDF.exe    # 最终可执行文件
```

## 注意事项

- 确保 `res/Spire.XLS.dll` 文件存在
- 生成的可执行文件包含了所有依赖项，可以在没有安装 .NET 的机器上运行
- 支持的 Excel 格式：.xls, .xlsx
- 程序需要 Windows 操作系统

## 系统要求

- Windows 7 或更高版本
- .NET 6.0 运行时（如果使用独立发布则不需要）
